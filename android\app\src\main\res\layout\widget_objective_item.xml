<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingVertical="4dp"
    android:paddingHorizontal="2dp">

    <!-- Checkbox -->
    <ImageView
        android:id="@+id/objective_checkbox"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_circle_outline"
        android:tint="@android:color/white"
        android:contentDescription="Complete objective"
        android:layout_marginEnd="8dp" />

    <!-- Objective content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Objective title -->
        <TextView
            android:id="@+id/objective_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Objective Title"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- Streak info -->
        <TextView
            android:id="@+id/objective_streak"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5 day streak"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:alpha="0.7"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
