<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingVertical="8dp"
    android:paddingHorizontal="4dp"
    android:background="@drawable/objective_item_background"
    android:layout_marginBottom="4dp">

    <!-- Checkbox -->
    <ImageView
        android:id="@+id/objective_checkbox"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_circle_outline"
        android:tint="#757575"
        android:contentDescription="Complete objective"
        android:layout_marginEnd="12dp" />

    <!-- Objective content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Objective title -->
        <TextView
            android:id="@+id/objective_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Objective Title"
            android:textColor="#1A1A1A"
            android:textSize="14sp"
            android:textStyle="normal"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- Streak info -->
        <TextView
            android:id="@+id/objective_streak"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5 day streak"
            android:textColor="#4CAF50"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
