import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:home_widget/home_widget.dart';
import '../models/objective.dart';

class WidgetService {
  static const String _appGroupId = 'group.com.example.day_1';
  static const String _androidWidgetName = 'QuickAddWidgetProvider';
  static const String _todayObjectivesWidgetName = 'TodayObjectivesWidgetProvider';
  static const MethodChannel _channel = MethodChannel('com.example.day_1/widget');
  static Function()? _onNavigateToAddObjective;
  static Function(int)? _onCompleteObjective;
  
  /// Initialize the widget service
  static Future<void> initialize() async {
    await HomeWidget.setAppGroupId(_appGroupId);
    _setupMethodChannel();
  }
  
  /// Setup method channel for widget interactions
  static void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'navigateToAddObjective':
          _onNavigateToAddObjective?.call();
          break;
        case 'completeObjective':
          final int objectiveId = call.arguments;
          _onCompleteObjective?.call(objectiveId);
          break;
      }
    });
  }
  
  /// Set callback for navigation to add objective screen
  static void setNavigateToAddObjectiveCallback(Function() callback) {
    _onNavigateToAddObjective = callback;
  }
  
  /// Set callback for completing an objective
  static void setCompleteObjectiveCallback(Function(int) callback) {
    _onCompleteObjective = callback;
  }
  
  /// Update the Quick Add Widget (1x1 version only needs minimal data)
  static Future<void> updateQuickAddWidget({
    required List<Objective> objectives,
  }) async {
    try {
      // Update the widget - no data needed for 1x1 widget
      await HomeWidget.updateWidget(
        name: _androidWidgetName,
        androidName: _androidWidgetName,
      );

    } catch (e) {
      debugPrint('Error updating Quick Add Widget: $e');
    }
  }

  /// Update the Today's Objectives Widget (2x2 version with objectives data)
  static Future<void> updateTodayObjectivesWidget({
    required List<Objective> objectives,
  }) async {
    try {
      // Filter objectives for today
      final today = DateTime.now();
      final todayObjectives = objectives.where((objective) {
        // Include objectives that are active (no end date or end date is in the future)
        final isActive = objective.endDate == null || objective.endDate!.isAfter(today);
        return isActive;
      }).toList();

      // Prepare data for the widget
      final objectivesData = todayObjectives.map((objective) => {
        'id': objective.id,
        'title': objective.title,
        'currentStreak': objective.currentStreak,
        'isCompleted': objective.isCompleteForToday(),
      }).toList();

      // Clear previous data first
      for (int i = 0; i < 4; i++) {
        await HomeWidget.saveWidgetData('objective_${i}_id', 0);
        await HomeWidget.saveWidgetData('objective_${i}_title', '');
        await HomeWidget.saveWidgetData('objective_${i}_streak', 0);
        await HomeWidget.saveWidgetData('objective_${i}_completed', false);
      }

      // Save individual objective data (only primitive types)
      for (int i = 0; i < objectivesData.length && i < 4; i++) {
        final objective = objectivesData[i];

        // Ensure all values are primitive types
        final id = objective['id'] as int? ?? 0;
        final title = objective['title'] as String? ?? '';
        final streak = objective['currentStreak'] as int? ?? 0;
        final completed = objective['isCompleted'] as bool? ?? false;

        await HomeWidget.saveWidgetData('objective_${i}_id', id);
        await HomeWidget.saveWidgetData('objective_${i}_title', title);
        await HomeWidget.saveWidgetData('objective_${i}_streak', streak);
        await HomeWidget.saveWidgetData('objective_${i}_completed', completed);
      }

      // Save count
      await HomeWidget.saveWidgetData('objectives_count', objectivesData.length);

      // Update the widget
      await HomeWidget.updateWidget(
        name: _todayObjectivesWidgetName,
        androidName: _todayObjectivesWidgetName,
      );

    } catch (e) {
      debugPrint('Error updating Today\'s Objectives Widget: $e');
    }
  }
  
  /// Handle widget interactions
  static Future<void> handleWidgetInteraction() async {
    try {
      HomeWidget.widgetClicked.listen((Uri? uri) {
        if (uri != null) {
          _handleWidgetClick(uri);
        }
      });
    } catch (e) {
      debugPrint('Error setting up widget interaction: $e');
    }
  }
  
  static void _handleWidgetClick(Uri uri) {
    debugPrint('Widget clicked with URI: $uri');
    
    // Handle different widget actions
    if (uri.host == 'add_objective') {
      // Navigate to add objective screen
      // This will be handled by the main app
    } else if (uri.host == 'complete_objective') {
      final objectiveId = int.tryParse(uri.queryParameters['id'] ?? '');
      if (objectiveId != null) {
        // Mark objective as complete
        // This will be handled by the main app
      }
    }
  }
  
  /// Get widget click stream
  static Stream<Uri?> get widgetClicked => HomeWidget.widgetClicked;
}