package com.example.day_1

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin

class TodayObjectivesWidgetProvider : AppWidgetProvider() {
    
    companion object {
        private const val ACTION_COMPLETE_OBJECTIVE = "com.example.day_1.COMPLETE_OBJECTIVE"
        private const val ACTION_ADD_OBJECTIVE = "com.example.day_1.ADD_OBJECTIVE"
        private const val EXTRA_OBJECTIVE_ID = "objective_id"
    }
    
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_COMPLETE_OBJECTIVE -> {
                val objectiveId = intent.getIntExtra(EXTRA_OBJECTIVE_ID, -1)
                if (objectiveId != -1) {
                    // Send completion to Flutter app
                    val completeIntent = Intent(context, MainActivity::class.java).apply {
                        data = Uri.parse("dayOne://complete_objective?id=$objectiveId")
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    }
                    context.startActivity(completeIntent)
                }
            }
            ACTION_ADD_OBJECTIVE -> {
                // Navigate to add objective screen
                val addIntent = Intent(context, MainActivity::class.java).apply {
                    data = Uri.parse("dayOne://add_objective")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
                context.startActivity(addIntent)
            }
        }
    }
    
    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.today_objectives_widget)
        
        // Get shared preferences data from Flutter
        val prefs = context.getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE)

        // Try to get objectives from individual keys first
        val objectives = getObjectivesFromPrefs(prefs)
        android.util.Log.d("TodayObjectivesWidget", "Loaded ${objectives.size} objectives from preferences")
        
        // Update widget content
        updateWidgetContent(context, views, objectives, appWidgetId)
        
        // Update the widget
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }
    
    private fun updateWidgetContent(
        context: Context,
        views: RemoteViews,
        objectives: List<ObjectiveData>,
        appWidgetId: Int
    ) {
        // Clear previous content
        views.removeAllViews(R.id.objectives_container)
        
        android.util.Log.d("TodayObjectivesWidget", "Updating widget content with ${objectives.size} objectives")

        // Use real objectives data
        val displayObjectives = objectives

        if (displayObjectives.isEmpty()) {
            // Show empty state
            views.setTextViewText(R.id.widget_title, "Today's Objectives")
            views.setViewVisibility(R.id.empty_message, android.view.View.VISIBLE)
            views.setViewVisibility(R.id.objectives_container, android.view.View.GONE)
            android.util.Log.d("TodayObjectivesWidget", "Showing empty state")
        } else {
            // Show objectives
            views.setTextViewText(R.id.widget_title, "Today's Objectives")
            views.setViewVisibility(R.id.empty_message, android.view.View.GONE)
            views.setViewVisibility(R.id.objectives_container, android.view.View.VISIBLE)
            
            // Add up to 4 objectives
            val limitedObjectives = displayObjectives.take(4)
            
            for ((index, objective) in limitedObjectives.withIndex()) {
                val objectiveView = RemoteViews(context.packageName, R.layout.widget_objective_item)
                
                // Set objective title
                objectiveView.setTextViewText(R.id.objective_title, objective.title)
                
                // Set streak text
                if (objective.currentStreak > 0) {
                    objectiveView.setTextViewText(R.id.objective_streak, "${objective.currentStreak} day streak")
                    objectiveView.setViewVisibility(R.id.objective_streak, android.view.View.VISIBLE)
                } else {
                    objectiveView.setViewVisibility(R.id.objective_streak, android.view.View.GONE)
                }
                
                // Set checkbox state
                if (objective.isCompleted) {
                    objectiveView.setImageViewResource(R.id.objective_checkbox, R.drawable.ic_check_circle)
                    objectiveView.setInt(R.id.objective_checkbox, "setColorFilter", android.graphics.Color.parseColor("#4CAF50"))
                } else {
                    objectiveView.setImageViewResource(R.id.objective_checkbox, R.drawable.ic_circle_outline)
                    objectiveView.setInt(R.id.objective_checkbox, "setColorFilter", android.graphics.Color.parseColor("#757575"))
                }
                
                // Set click listener for checkbox
                if (!objective.isCompleted) {
                    val completeIntent = Intent(context, TodayObjectivesWidgetProvider::class.java).apply {
                        action = ACTION_COMPLETE_OBJECTIVE
                        putExtra(EXTRA_OBJECTIVE_ID, objective.id)
                    }
                    val completePendingIntent = PendingIntent.getBroadcast(
                        context,
                        objective.id,
                        completeIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                    objectiveView.setOnClickPendingIntent(R.id.objective_checkbox, completePendingIntent)
                }
                
                views.addView(R.id.objectives_container, objectiveView)
            }
        }
        
        // Set up add objective button
        val addIntent = Intent(context, TodayObjectivesWidgetProvider::class.java).apply {
            action = ACTION_ADD_OBJECTIVE
        }
        val addPendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            addIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.add_objective_button, addPendingIntent)
    }
    
    private fun getObjectivesFromPrefs(prefs: android.content.SharedPreferences): List<ObjectiveData> {
        val objectives = mutableListOf<ObjectiveData>()

        try {
            val count = prefs.getInt("flutter.objectives_count", 0)
            android.util.Log.d("TodayObjectivesWidget", "Objectives count from prefs: $count")

            for (i in 0 until minOf(count, 4)) {
                val id = prefs.getInt("flutter.objective_${i}_id", 0)
                val title = prefs.getString("flutter.objective_${i}_title", "") ?: ""
                val streak = prefs.getInt("flutter.objective_${i}_streak", 0)
                val completed = prefs.getBoolean("flutter.objective_${i}_completed", false)

                android.util.Log.d("TodayObjectivesWidget", "Objective $i: id=$id, title=$title, streak=$streak, completed=$completed")

                if (id > 0 && title.isNotEmpty()) {
                    objectives.add(ObjectiveData(id, title, streak, completed))
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("TodayObjectivesWidget", "Error reading objectives from prefs: $e")
        }

        return objectives
    }

    private fun parseObjectivesJson(json: String): List<ObjectiveData> {
        val objectives = mutableListOf<ObjectiveData>()

        try {
            if (json.isBlank() || json == "[]") {
                return objectives
            }

            // Simple parsing - look for patterns in the JSON string
            // This is a basic implementation that works with Flutter's data format
            val regex = Regex(""""id":(\d+).*?"title":"([^"]*)".*?"currentStreak":(\d+).*?"isCompleted":(true|false)""")
            val matches = regex.findAll(json)

            for (match in matches) {
                val id = match.groupValues[1].toIntOrNull() ?: 0
                val title = match.groupValues[2]
                val currentStreak = match.groupValues[3].toIntOrNull() ?: 0
                val isCompleted = match.groupValues[4].toBoolean()

                if (id > 0 && title.isNotEmpty()) {
                    objectives.add(ObjectiveData(id, title, currentStreak, isCompleted))
                }
            }
        } catch (e: Exception) {
            // If parsing fails, return empty list
            android.util.Log.e("TodayObjectivesWidget", "Error parsing objectives JSON: $e")
        }

        return objectives
    }
    
    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }
    
    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }
}

data class ObjectiveData(
    val id: Int,
    val title: String,
    val currentStreak: Int,
    val isCompleted: Boolean
)
