import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/objective.dart';
import '../providers/objectives_provider.dart';
import '../services/widget_service.dart';
import 'add_objective_screen.dart';
import 'objective_detail_screen.dart';
import '../widgets/objective_card.dart';
import '../widgets/empty_state.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  Objective? _selectedObjective;

  @override
  void initState() {
    super.initState();
    
    // Load objectives when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ObjectivesProvider>(context, listen: false).loadObjectives();
      Provider.of<ObjectivesProvider>(context, listen: false).checkStreaks();
    });
  }

  @override
  Widget build(BuildContext context) {
    final objectivesProvider = Provider.of<ObjectivesProvider>(context);
    final objectives = objectivesProvider.objectives;
    
    // Update selected objective if needed
    if (_selectedObjective == null && objectives.isNotEmpty) {
      _selectedObjective = objectives.first;
    } else if (_selectedObjective != null) {
      // Make sure the selected objective still exists in the list (may have been deleted)
      final stillExists = objectives.any((obj) => obj.id == _selectedObjective!.id);
      if (!stillExists && objectives.isNotEmpty) {
        _selectedObjective = objectives.first;
      } else if (!stillExists) {
        _selectedObjective = null;
      } else {
        // Update the selected objective with fresh data
        _selectedObjective = objectives.firstWhere((obj) => obj.id == _selectedObjective!.id);
      }
    }
    
    // Check if the screen is in landscape orientation
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Day One'),
        centerTitle: true,
        automaticallyImplyLeading: false, // Remove back button since this is a tab
        actions: [
          IconButton(
            icon: const Icon(Icons.widgets),
            onPressed: () async {
              final provider = Provider.of<ObjectivesProvider>(context, listen: false);
              await WidgetService.updateTodayObjectivesWidget(objectives: provider.objectives);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Widget updated!')),
              );
            },
            tooltip: 'Update Widget',
          ),
        ],
      ),
      body: objectivesProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : objectives.isEmpty
              ? const EmptyState(
                  message: 'No objectives yet',
                  subMessage: 'Tap the + button to add a new objective',
                )
              : isLandscape
                  ? _buildLandscapeLayout(objectives)
                  : _buildObjectivesList(objectives),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddObjective(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildLandscapeLayout(List<Objective> objectives) {
    return Row(
      children: [
        // Side panel showing objective titles (takes ~30% of width)
        Container(
          width: MediaQuery.of(context).size.width * 0.3,
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: _buildObjectiveTitlesList(objectives),
        ),
        
        // Main content area showing selected objective details
        Expanded(
          child: _buildExpandedObjective(_selectedObjective),
        ),
      ],
    );
  }
  
  Widget _buildObjectiveTitlesList(List<Objective> objectives) {
    return RefreshIndicator(
      onRefresh: () async {
        await Provider.of<ObjectivesProvider>(context, listen: false).loadObjectives();
      },
      child: ReorderableListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: objectives.length,
        onReorder: (oldIndex, newIndex) {
          Provider.of<ObjectivesProvider>(context, listen: false).reorderObjectives(oldIndex, newIndex);
        },
        proxyDecorator: (child, index, animation) {
          return Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final objective = objectives[index];
          final isSelected = _selectedObjective?.id == objective.id;
          
          return Card(
            key: ValueKey(objective.id),
            margin: const EdgeInsets.only(bottom: 8),
            elevation: isSelected ? 4 : 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color: isSelected 
                  ? Theme.of(context).colorScheme.primary
                  : Color(objective.color),
                width: isSelected ? 2 : 3,
              ),
            ),
            child: ListTile(
              title: Text(
                objective.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Theme.of(context).colorScheme.primary : null,
                ),
              ),
              leading: objective.isCompleteForToday() 
                ? Icon(Icons.check_circle, color: Theme.of(context).colorScheme.primary)
                : Icon(Icons.circle_outlined),
              trailing: Icon(Icons.drag_handle, size: 20),
              onTap: () {
                setState(() {
                  _selectedObjective = objective;
                });
              },
              tileColor: isSelected 
                ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.15)
                : null,
              dense: true,
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildExpandedObjective(Objective? objective) {
    if (objective == null) {
      return const Center(child: Text('Select an objective'));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  objective.title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Checkbox(
                value: objective.isCompleteForToday(),
                onChanged: (value) {
                  if (value != null) {
                    _markObjectiveComplete(context, objective.id!);
                  }
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (objective.description.isNotEmpty) ...[
            Text(
              objective.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 20),
          ],
          
          // Date information
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 8),
              Text(
                'Started: ${objective.formattedStartDate}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              
              if (objective.endDate != null) ...[
                const SizedBox(width: 24),
                Icon(
                  Icons.event,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  'Ends: ${objective.formattedEndDate}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: objective.isExpired ? Colors.red : null,
                    fontWeight: objective.isExpired ? FontWeight.bold : null,
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Progress section (if applicable)
          if (objective.endDate != null && objective.completionPercentage != null) ...[
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: LinearProgressIndicator(
                          value: objective.completionPercentage! / 100,
                          minHeight: 10,
                          backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(objective.color)),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${objective.completionPercentage!.toStringAsFixed(0)}%',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
          
          // Streak information
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStreakCard(
                context, 
                'Current Streak', 
                objective.currentStreak.toString(), 
                Icons.local_fire_department, 
                Colors.orange
              ),
              _buildStreakCard(
                context, 
                'Best Streak', 
                objective.longestStreak.toString(), 
                Icons.emoji_events, 
                Colors.amber
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _navigateToObjectiveDetail(context, objective),
              icon: const Icon(Icons.visibility),
              label: const Text('View Details'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStreakCard(BuildContext context, String label, String value, IconData icon, Color color) {
    final isZero = value == '0';
    final activeColor = isZero ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5) : color;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Column(
          children: [
            Icon(icon, color: activeColor, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: activeColor,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectivesList(List<Objective> objectives) {
    return RefreshIndicator(
      onRefresh: () async {
        await Provider.of<ObjectivesProvider>(context, listen: false).loadObjectives();
      },
      child: ReorderableListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: objectives.length,
        onReorder: (oldIndex, newIndex) {
          Provider.of<ObjectivesProvider>(context, listen: false).reorderObjectives(oldIndex, newIndex);
        },
        proxyDecorator: (child, index, animation) {
          return Material(
            elevation: 8.0,
            borderRadius: BorderRadius.circular(12),
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final objective = objectives[index];
          return Padding(
            key: ValueKey(objective.id),
            padding: const EdgeInsets.only(bottom: 12),
            child: ObjectiveCard(
              objective: objective,
              onTap: () => _navigateToObjectiveDetail(context, objective),
              onCompleteToggle: (isComplete) => _markObjectiveComplete(context, objective.id!),
              isDragging: false,
            ),
          );
        },
      ),
    );
  }

  void _markObjectiveComplete(BuildContext context, int objectiveId) {
    Provider.of<ObjectivesProvider>(context, listen: false)
        .markObjectiveComplete(objectiveId);
  }

  void _navigateToAddObjective(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddObjectiveScreen(),
      ),
    );

    if (result == true) {
      // Refresh the list if an objective was added
      Provider.of<ObjectivesProvider>(context, listen: false).loadObjectives();
    }
  }

  void _navigateToObjectiveDetail(BuildContext context, Objective objective) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ObjectiveDetailScreen(objective: objective),
      ),
    );

    if (result == true) {
      // Refresh the list if the objective was updated or deleted
      Provider.of<ObjectivesProvider>(context, listen: false).loadObjectives();
    }
  }
}