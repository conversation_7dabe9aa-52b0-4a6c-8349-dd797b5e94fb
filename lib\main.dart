import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/objectives_provider.dart';
import 'screens/main_navigation_screen.dart';
import 'screens/add_objective_screen.dart';
import 'services/theme_service.dart';
import 'services/notification_service.dart';
import 'services/widget_service.dart';
import 'package:home_widget/home_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize NotificationService
  final notificationService = NotificationService();
  await notificationService.initialize();
  await notificationService.requestPermissions();
  
  // Initialize ThemeService
  final themeService = ThemeService();
  await themeService.loadTheme();
  
  // Initialize WidgetService
  await WidgetService.initialize();
  await WidgetService.handleWidgetInteraction();
  
  // Initialize widgets with empty data
  await WidgetService.updateQuickAddWidget(objectives: []);
  await WidgetService.updateTodayObjectivesWidget(objectives: []);
  
  runApp(MyApp(themeService: themeService));
}

class MyApp extends StatefulWidget {
  final ThemeService themeService;
  const MyApp({super.key, required this.themeService});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    _setupWidgetInteraction();
  }

  void _setupWidgetInteraction() {
    WidgetService.widgetClicked.listen((Uri? uri) {
      if (uri != null) {
        _handleWidgetClick(uri);
      }
    });
  }

  void _handleWidgetClick(Uri uri) {
    if (uri.host == 'add_objective') {
      _navigatorKey.currentState?.push(
        MaterialPageRoute(
          builder: (context) => const AddObjectiveScreen(),
        ),
      );
    } else if (uri.host == 'complete_objective') {
      final objectiveId = int.tryParse(uri.queryParameters['id'] ?? '');
      if (objectiveId != null) {
        // Handle objective completion
        // This could trigger a provider method to mark as complete
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: widget.themeService),
        ChangeNotifierProvider(create: (_) => ObjectivesProvider()),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, _) {
          return MaterialApp(
            navigatorKey: _navigatorKey,
            title: 'Day One',
            debugShowCheckedModeBanner: false,
            theme: ThemeService.lightTheme,
            darkTheme: ThemeService.darkTheme,
            themeMode: themeService.themeMode,
            home: const MainNavigationScreen(),
          );
        },
      ),
    );
  }
}
