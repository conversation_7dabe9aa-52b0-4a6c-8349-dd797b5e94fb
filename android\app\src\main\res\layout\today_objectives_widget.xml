<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/app_widget_background">

    <!-- Header with title and add button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <TextView
            android:id="@+id/widget_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Today's Objectives"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <ImageButton
            android:id="@+id/add_objective_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_add"
            android:background="@drawable/button_background"
            android:tint="@android:color/white"
            android:contentDescription="Add New Objective"
            android:scaleType="centerInside" />

    </LinearLayout>

    <!-- Objectives container -->
    <LinearLayout
        android:id="@+id/objectives_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="visible" />

    <!-- Empty state message -->
    <TextView
        android:id="@+id/empty_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="Tap + to add your first objective"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
